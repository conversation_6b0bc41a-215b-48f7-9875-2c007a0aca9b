export declare class ChromeBridge {
    private wss;
    private chromeConnection;
    private port;
    private pendingCalls;
    private isStarted;
    start(): Promise<void>;
    stop(): Promise<void>;
    private setupWebSocketHandlers;
    private handleMessage;
    private handleToolResponse;
    executeTool(toolName: string, args: any): Promise<any>;
    ping(): Promise<boolean>;
    isConnected(): boolean;
    getStatus(): {
        isStarted: boolean;
        isConnected: boolean;
        port: number;
        pendingCalls: number;
    };
}
//# sourceMappingURL=bridge.d.ts.map