{"version": 3, "file": "bridge.js", "sourceRoot": "", "sources": ["../src/bridge.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAAgD;AAChD,+BAAoC;AAWpC,MAAa,YAAY;IACb,GAAG,GAA2B,IAAI,CAAC;IACnC,gBAAgB,GAAqB,IAAI,CAAC;IAC1C,IAAI,GAAG,IAAI,CAAC;IACZ,YAAY,GAAG,IAAI,GAAG,EAAoB,CAAC;IAC3C,SAAS,GAAG,KAAK,CAAC;IAE1B,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,iBAAiB;YACjB,IAAI,CAAC,GAAG,GAAG,IAAI,oBAAe,CAAC;gBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE;gBAC7B,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAC5C,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;gBAC3B,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC3B,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,iCAAiC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACN,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,aAAa;QACb,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAE1B,gBAAgB;QAChB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;IAEO,sBAAsB,CAAC,EAAa;QACxC,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE;YACtB,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAChB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAE7B,aAAa;YACb,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACzC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;YAC5D,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACrB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACnB,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC,CAAC;IACR,CAAC;IAEO,aAAa,CAAC,OAAY;QAC9B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM;gBACP,WAAW;gBACX,MAAM;YAEV,KAAK,eAAe;gBAChB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM;YAEV;gBACI,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAY;QACnC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAC/B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEvC,IAAI,IAAI,EAAE,CAAC;YACP,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,IAAS;QACzC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE,CAAC;YAChF,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,EAAE,GAAG,IAAA,SAAM,GAAE,CAAC;YAEpB,OAAO;YACP,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC7B,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC,CAAC;YACxD,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ;YAEnB,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE;gBACtB,EAAE;gBACF,QAAQ;gBACR,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,OAAO;aACV,CAAC,CAAC;YAEH,WAAW;YACX,MAAM,OAAO,GAAG;gBACZ,IAAI,EAAE,WAAW;gBACjB,EAAE;gBACF,QAAQ;gBACR,IAAI;aACP,CAAC;YAEF,IAAI,CAAC;gBACD,IAAI,CAAC,gBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACzD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC7B,MAAM,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,IAAI;QACN,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK,YAAS,CAAC,IAAI,EAAE,CAAC;YAChF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,EAAE,IAAI,CAAC,CAAC;YAET,MAAM,UAAU,GAAG,CAAC,IAAY,EAAE,EAAE;gBAChC,IAAI,CAAC;oBACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC5C,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wBAC7D,YAAY,CAAC,OAAO,CAAC,CAAC;wBACtB,IAAI,CAAC,gBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;wBAClD,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,SAAS;gBACb,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,gBAAiB,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAEjD,IAAI,CAAC,gBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;gBACvC,IAAI,EAAE,MAAM;gBACZ,SAAS;aACZ,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC;IACP,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,gBAAgB,KAAK,IAAI;YAC9B,IAAI,CAAC,gBAAgB,CAAC,UAAU,KAAK,YAAS,CAAC,IAAI,CAAC;IAC/D,CAAC;IAED,SAAS;QACL,OAAO;YACH,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;SACvC,CAAC;IACN,CAAC;CACJ;AAtND,oCAsNC"}