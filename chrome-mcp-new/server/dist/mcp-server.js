"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChromeMCPServer = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const bridge_js_1 = require("./bridge.js");
class ChromeMCPServer {
    server;
    bridge;
    constructor() {
        this.server = new index_js_1.Server({
            name: 'chrome-mcp-augment',
            version: '1.0.0',
        }, {
            capabilities: {
                tools: {},
            },
        });
        this.bridge = new bridge_js_1.ChromeBridge();
        this.setupHandlers();
    }
    setupHandlers() {
        // 处理工具列表请求
        this.server.setRequestHandler(types_js_1.ListToolsRequestSchema, async () => {
            return {
                tools: this.getToolDefinitions(),
            };
        });
        // 处理工具调用请求
        this.server.setRequestHandler(types_js_1.CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                const result = await this.bridge.executeTool(name, args || {});
                return result;
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error executing tool ${name}: ${errorMessage}`,
                        },
                    ],
                    isError: true,
                };
            }
        });
    }
    getToolDefinitions() {
        return [
            {
                name: 'get_windows_and_tabs',
                description: 'Get information about all browser windows and tabs',
                inputSchema: {
                    type: 'object',
                    properties: {},
                },
            },
            {
                name: 'chrome_navigate',
                description: 'Navigate to a URL in the browser',
                inputSchema: {
                    type: 'object',
                    properties: {
                        url: {
                            type: 'string',
                            description: 'The URL to navigate to',
                        },
                        newWindow: {
                            type: 'boolean',
                            description: 'Whether to open in a new window',
                            default: false,
                        },
                        width: {
                            type: 'number',
                            description: 'Window width (if opening new window)',
                        },
                        height: {
                            type: 'number',
                            description: 'Window height (if opening new window)',
                        },
                        refresh: {
                            type: 'boolean',
                            description: 'Whether to refresh the current tab instead',
                            default: false,
                        },
                    },
                    required: [],
                },
            },
            {
                name: 'chrome_close_tabs',
                description: 'Close browser tabs by ID or URL pattern',
                inputSchema: {
                    type: 'object',
                    properties: {
                        tabIds: {
                            type: 'array',
                            items: { type: 'number' },
                            description: 'Array of tab IDs to close',
                        },
                        url: {
                            type: 'string',
                            description: 'URL pattern to match tabs to close',
                        },
                    },
                },
            },
            {
                name: 'chrome_go_back_or_forward',
                description: 'Navigate back or forward in browser history',
                inputSchema: {
                    type: 'object',
                    properties: {
                        isForward: {
                            type: 'boolean',
                            description: 'Whether to go forward (true) or back (false)',
                            default: false,
                        },
                    },
                },
            },
            {
                name: 'chrome_screenshot',
                description: 'Take a screenshot of the current page or specific element',
                inputSchema: {
                    type: 'object',
                    properties: {
                        fullPage: {
                            type: 'boolean',
                            description: 'Whether to capture the full page',
                            default: false,
                        },
                        selector: {
                            type: 'string',
                            description: 'CSS selector of element to screenshot',
                        },
                        quality: {
                            type: 'number',
                            description: 'Image quality (0-100)',
                            default: 90,
                        },
                    },
                },
            },
            {
                name: 'chrome_get_web_content',
                description: 'Extract text or HTML content from the current page',
                inputSchema: {
                    type: 'object',
                    properties: {
                        format: {
                            type: 'string',
                            enum: ['text', 'html'],
                            description: 'Content format to extract',
                            default: 'text',
                        },
                        selector: {
                            type: 'string',
                            description: 'CSS selector to extract content from specific element',
                        },
                    },
                },
            },
            {
                name: 'chrome_click_element',
                description: 'Click on an element in the page',
                inputSchema: {
                    type: 'object',
                    properties: {
                        selector: {
                            type: 'string',
                            description: 'CSS selector of the element to click',
                        },
                        waitTime: {
                            type: 'number',
                            description: 'Time to wait after clicking (ms)',
                            default: 1000,
                        },
                    },
                    required: ['selector'],
                },
            },
            {
                name: 'chrome_fill_or_select',
                description: 'Fill input fields or select options',
                inputSchema: {
                    type: 'object',
                    properties: {
                        selector: {
                            type: 'string',
                            description: 'CSS selector of the input element',
                        },
                        value: {
                            type: 'string',
                            description: 'Value to fill or select',
                        },
                        clear: {
                            type: 'boolean',
                            description: 'Whether to clear the field first',
                            default: true,
                        },
                    },
                    required: ['selector', 'value'],
                },
            },
            {
                name: 'chrome_keyboard',
                description: 'Send keyboard input to the page',
                inputSchema: {
                    type: 'object',
                    properties: {
                        keys: {
                            type: 'string',
                            description: 'Keys to send (e.g., "Enter", "Ctrl+A")',
                        },
                        selector: {
                            type: 'string',
                            description: 'CSS selector of element to focus first',
                        },
                    },
                    required: ['keys'],
                },
            },
            {
                name: 'chrome_history',
                description: 'Search browser history',
                inputSchema: {
                    type: 'object',
                    properties: {
                        query: {
                            type: 'string',
                            description: 'Search query for history',
                        },
                        maxResults: {
                            type: 'number',
                            description: 'Maximum number of results',
                            default: 50,
                        },
                        startTime: {
                            type: 'number',
                            description: 'Start time (timestamp)',
                        },
                        endTime: {
                            type: 'number',
                            description: 'End time (timestamp)',
                        },
                    },
                },
            },
            {
                name: 'chrome_bookmark_search',
                description: 'Search bookmarks',
                inputSchema: {
                    type: 'object',
                    properties: {
                        query: {
                            type: 'string',
                            description: 'Search query for bookmarks',
                        },
                    },
                },
            },
            {
                name: 'chrome_bookmark_add',
                description: 'Add a new bookmark',
                inputSchema: {
                    type: 'object',
                    properties: {
                        title: {
                            type: 'string',
                            description: 'Bookmark title',
                        },
                        url: {
                            type: 'string',
                            description: 'Bookmark URL',
                        },
                        parentId: {
                            type: 'string',
                            description: 'Parent folder ID',
                        },
                    },
                    required: ['title', 'url'],
                },
            },
            {
                name: 'search_tabs_content',
                description: 'Search content across all open tabs using semantic similarity',
                inputSchema: {
                    type: 'object',
                    properties: {
                        query: {
                            type: 'string',
                            description: 'Search query',
                        },
                        maxResults: {
                            type: 'number',
                            description: 'Maximum number of results',
                            default: 10,
                        },
                        threshold: {
                            type: 'number',
                            description: 'Similarity threshold (0-1)',
                            default: 0.7,
                        },
                    },
                    required: ['query'],
                },
            },
        ];
    }
    async run() {
        // 启动Chrome桥接器
        await this.bridge.start();
        // 启动MCP服务器
        const transport = new stdio_js_1.StdioServerTransport();
        await this.server.connect(transport);
        console.error('Chrome MCP Server started and ready for connections');
    }
    async close() {
        await this.bridge.stop();
        await this.server.close();
    }
}
exports.ChromeMCPServer = ChromeMCPServer;
//# sourceMappingURL=mcp-server.js.map