import WebSocket, { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';

interface ToolCall {
    id: string;
    toolName: string;
    args: any;
    resolve: (result: any) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
}

export class ChromeBridge {
    private wss: WebSocketServer | null = null;
    private chromeConnection: WebSocket | null = null;
    private port = 18765;
    private pendingCalls = new Map<string, ToolCall>();
    private isStarted = false;

    async start() {
        if (this.isStarted) {
            return;
        }

        try {
            // 创建WebSocket服务器
            this.wss = new WebSocketServer({ 
                port: this.port,
                host: 'localhost'
            });

            this.wss.on('connection', (ws) => {
                console.error(`Chrome extension connected`);
                this.chromeConnection = ws;
                this.setupWebSocketHandlers(ws);
            });

            this.wss.on('error', (error) => {
                console.error('WebSocket server error:', error);
            });

            this.isStarted = true;
            console.error(`Chrome bridge started on port ${this.port}`);
        } catch (error) {
            console.error('Failed to start Chrome bridge:', error);
            throw error;
        }
    }

    async stop() {
        if (!this.isStarted) {
            return;
        }

        // 清理所有待处理的调用
        for (const [id, call] of this.pendingCalls) {
            clearTimeout(call.timeout);
            call.reject(new Error('Bridge shutting down'));
        }
        this.pendingCalls.clear();

        // 关闭WebSocket连接
        if (this.chromeConnection) {
            this.chromeConnection.close();
            this.chromeConnection = null;
        }

        // 关闭WebSocket服务器
        if (this.wss) {
            this.wss.close();
            this.wss = null;
        }

        this.isStarted = false;
        console.error('Chrome bridge stopped');
    }

    private setupWebSocketHandlers(ws: WebSocket) {
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleMessage(message);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        });

        ws.on('close', () => {
            console.error('Chrome extension disconnected');
            this.chromeConnection = null;
            
            // 拒绝所有待处理的调用
            for (const [id, call] of this.pendingCalls) {
                clearTimeout(call.timeout);
                call.reject(new Error('Chrome extension disconnected'));
            }
            this.pendingCalls.clear();
        });

        ws.on('error', (error) => {
            console.error('WebSocket connection error:', error);
        });

        // 发送连接确认
        ws.send(JSON.stringify({
            type: 'connected',
            timestamp: Date.now()
        }));
    }

    private handleMessage(message: any) {
        switch (message.type) {
            case 'pong':
                // 处理ping响应
                break;
            
            case 'tool_response':
                this.handleToolResponse(message);
                break;
            
            default:
                console.error('Unknown message type:', message.type);
        }
    }

    private handleToolResponse(message: any) {
        const { id, result } = message;
        const call = this.pendingCalls.get(id);
        
        if (call) {
            clearTimeout(call.timeout);
            this.pendingCalls.delete(id);
            call.resolve(result);
        } else {
            console.error('Received response for unknown call ID:', id);
        }
    }

    async executeTool(toolName: string, args: any): Promise<any> {
        if (!this.chromeConnection || this.chromeConnection.readyState !== WebSocket.OPEN) {
            throw new Error('Chrome extension not connected');
        }

        return new Promise((resolve, reject) => {
            const id = uuidv4();
            
            // 设置超时
            const timeout = setTimeout(() => {
                this.pendingCalls.delete(id);
                reject(new Error(`Tool call timeout: ${toolName}`));
            }, 30000); // 30秒超时

            // 存储调用信息
            this.pendingCalls.set(id, {
                id,
                toolName,
                args,
                resolve,
                reject,
                timeout
            });

            // 发送工具调用请求
            const message = {
                type: 'tool_call',
                id,
                toolName,
                args
            };

            try {
                this.chromeConnection!.send(JSON.stringify(message));
            } catch (error) {
                clearTimeout(timeout);
                this.pendingCalls.delete(id);
                reject(error);
            }
        });
    }

    async ping(): Promise<boolean> {
        if (!this.chromeConnection || this.chromeConnection.readyState !== WebSocket.OPEN) {
            return false;
        }

        return new Promise((resolve) => {
            const timestamp = Date.now();
            const timeout = setTimeout(() => {
                resolve(false);
            }, 5000);

            const handlePong = (data: Buffer) => {
                try {
                    const message = JSON.parse(data.toString());
                    if (message.type === 'pong' && message.timestamp === timestamp) {
                        clearTimeout(timeout);
                        this.chromeConnection!.off('message', handlePong);
                        resolve(true);
                    }
                } catch (error) {
                    // 忽略解析错误
                }
            };

            this.chromeConnection!.on('message', handlePong);

            this.chromeConnection!.send(JSON.stringify({
                type: 'ping',
                timestamp
            }));
        });
    }

    isConnected(): boolean {
        return this.chromeConnection !== null && 
               this.chromeConnection.readyState === WebSocket.OPEN;
    }

    getStatus() {
        return {
            isStarted: this.isStarted,
            isConnected: this.isConnected(),
            port: this.port,
            pendingCalls: this.pendingCalls.size
        };
    }
}
