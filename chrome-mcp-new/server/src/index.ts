#!/usr/bin/env node

import { ChromeMCPServer } from './mcp-server.js';

async function main() {
    const server = new ChromeMCPServer();
    
    // 处理进程退出
    process.on('SIGINT', async () => {
        console.error('Received SIGINT, shutting down gracefully...');
        await server.close();
        process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
        console.error('Received SIGTERM, shutting down gracefully...');
        await server.close();
        process.exit(0);
    });
    
    try {
        await server.run();
    } catch (error) {
        console.error('Server error:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch((error) => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}
