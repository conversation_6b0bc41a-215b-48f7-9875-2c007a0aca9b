#!/bin/bash

# Chrome MCP Server启动脚本

echo "🚀 启动Chrome MCP Server for Augment..."

# 检查Node.js版本
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js 18+"
    exit 1
fi

echo "✓ Node.js版本: $NODE_VERSION"

# 检查是否在正确的目录
if [ ! -f "server/package.json" ]; then
    echo "❌ 错误: 请在chrome-mcp-new目录中运行此脚本"
    exit 1
fi

# 安装依赖（如果需要）
if [ ! -d "server/node_modules" ]; then
    echo "📦 安装依赖..."
    cd server
    npm install
    cd ..
fi

# 构建项目（如果需要）
if [ ! -d "server/dist" ]; then
    echo "🔨 构建项目..."
    cd server
    npm run build
    cd ..
fi

# 检查Chrome扩展是否已加载
echo "📋 请确保已完成以下步骤:"
echo "   1. 在Chrome中加载了extension文件夹作为扩展"
echo "   2. 在Augment设置中配置了MCP服务器"
echo "   3. Chrome扩展popup显示准备连接状态"
echo ""

# 启动服务器
echo "🌟 启动MCP服务器..."
echo "   - WebSocket端口: 18765"
echo "   - MCP协议: stdio"
echo "   - 按Ctrl+C停止服务器"
echo ""

cd server
exec node dist/index.js
