#!/usr/bin/env node

// Simple test script to verify the MCP server functionality
const { spawn } = require('child_process');
const WebSocket = require('ws');

class MCPTester {
    constructor() {
        this.serverProcess = null;
        this.wsClient = null;
    }

    async startServer() {
        console.log('Starting MCP server...');
        
        this.serverProcess = spawn('node', ['server/dist/index.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        this.serverProcess.stdout.on('data', (data) => {
            console.log('Server stdout:', data.toString());
        });

        this.serverProcess.stderr.on('data', (data) => {
            console.log('Server stderr:', data.toString());
        });

        this.serverProcess.on('close', (code) => {
            console.log(`Server process exited with code ${code}`);
        });

        // Wait a bit for server to start
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    async testWebSocketConnection() {
        console.log('Testing WebSocket connection...');
        
        return new Promise((resolve, reject) => {
            this.wsClient = new WebSocket('ws://localhost:8080/ws');
            
            this.wsClient.on('open', () => {
                console.log('✓ WebSocket connection established');
                resolve(true);
            });
            
            this.wsClient.on('error', (error) => {
                console.log('✗ WebSocket connection failed:', error.message);
                reject(error);
            });
            
            this.wsClient.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    console.log('Received message:', message);
                } catch (error) {
                    console.log('Received non-JSON message:', data.toString());
                }
            });
        });
    }

    async testMCPProtocol() {
        console.log('Testing MCP protocol...');
        
        // Test initialize
        const initMessage = {
            jsonrpc: '2.0',
            id: 1,
            method: 'initialize',
            params: {
                protocolVersion: '2024-11-05',
                capabilities: {},
                clientInfo: {
                    name: 'test-client',
                    version: '1.0.0'
                }
            }
        };

        this.sendMCPMessage(initMessage);

        // Test list tools
        const listToolsMessage = {
            jsonrpc: '2.0',
            id: 2,
            method: 'tools/list',
            params: {}
        };

        this.sendMCPMessage(listToolsMessage);
    }

    sendMCPMessage(message) {
        if (this.serverProcess && this.serverProcess.stdin) {
            const messageStr = JSON.stringify(message) + '\n';
            console.log('Sending MCP message:', messageStr.trim());
            this.serverProcess.stdin.write(messageStr);
        }
    }

    async testToolCall() {
        console.log('Testing tool call...');
        
        const toolCallMessage = {
            jsonrpc: '2.0',
            id: 3,
            method: 'tools/call',
            params: {
                name: 'get_windows_and_tabs',
                arguments: {}
            }
        };

        this.sendMCPMessage(toolCallMessage);
    }

    async cleanup() {
        console.log('Cleaning up...');
        
        if (this.wsClient) {
            this.wsClient.close();
        }
        
        if (this.serverProcess) {
            this.serverProcess.kill();
        }
    }

    async run() {
        try {
            await this.startServer();
            
            // Test WebSocket connection (this will fail until Chrome extension connects)
            try {
                await this.testWebSocketConnection();
            } catch (error) {
                console.log('WebSocket test failed (expected if Chrome extension not connected)');
            }
            
            // Test MCP protocol
            await this.testMCPProtocol();
            
            // Wait a bit for responses
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Test tool call
            await this.testToolCall();
            
            // Wait for responses
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error('Test failed:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the test
if (require.main === module) {
    const tester = new MCPTester();
    tester.run().then(() => {
        console.log('Test completed');
        process.exit(0);
    }).catch((error) => {
        console.error('Test failed:', error);
        process.exit(1);
    });
}
