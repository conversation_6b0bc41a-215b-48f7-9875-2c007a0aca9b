# Chrome MCP Server for Augment

这是一个专为Augment MCP接口设计的Chrome浏览器扩展，完全兼容Augment的MCP协议，同时保持原mcp-chrome项目的所有功能。

## 项目架构

```
chrome-mcp-new/
├── extension/          # Chrome扩展文件
│   ├── manifest.json   # 扩展配置文件
│   ├── background.js   # 后台脚本
│   ├── popup.html      # 弹出窗口界面
│   ├── popup.js        # 弹出窗口脚本
│   ├── content-scripts/ # 内容脚本
│   ├── inject-scripts/  # 注入脚本
│   ├── assets/         # 静态资源
│   └── icon/           # 图标文件
├── server/            # MCP服务器
│   ├── src/
│   │   ├── index.ts    # 服务器入口
│   │   ├── mcp-server.ts # MCP协议实现
│   │   ├── tools/      # 工具实现
│   │   └── bridge.ts   # 通信桥梁
│   ├── package.json
│   └── tsconfig.json
└── README.md
```

## 技术方案

### 通信架构
1. **Augment ↔ MCP服务器**: 通过stdio进行标准MCP协议通信
2. **MCP服务器 ↔ Chrome扩展**: 通过WebSocket进行实时通信
3. **Chrome扩展**: 执行具体的浏览器操作

### 核心功能
- 🌐 **20+浏览器工具**: 导航、截图、网络监控、内容分析等
- 🧠 **语义搜索**: 内置向量数据库，智能搜索浏览器标签页内容
- 🔍 **智能内容分析**: AI驱动的文本提取和相似度匹配
- 🚀 **SIMD加速**: 自定义WebAssembly SIMD优化，向量操作速度提升4-8倍

## 安装使用

### 前置要求
- Node.js 18+
- Chrome/Chromium浏览器
- Augment VS Code插件

### 安装步骤

#### 1. 安装MCP服务器依赖
```bash
cd chrome-mcp-new/server
npm install
npm run build
```

#### 2. 加载Chrome扩展
1. 打开Chrome浏览器，访问 `chrome://extensions/`
2. 开启"开发者模式"（右上角开关）
3. 点击"加载已解压的扩展程序"
4. 选择 `chrome-mcp-new/extension` 文件夹
5. 扩展安装完成后，点击扩展图标打开popup界面
6. 点击"Connect to MCP Server"按钮

#### 3. 配置Augment MCP设置
1. 在VS Code中打开Augment设置面板（点击Augment面板右上角的齿轮图标）
2. 在MCP服务器部分添加新的服务器配置：
   - **Name**: `chrome-mcp-augment`
   - **Command**: `node`
   - **Args**: `["/path/to/chrome-mcp-new/server/dist/index.js"]`

   或者直接编辑settings.json：
   ```json
   {
     "augment.advanced": {
       "mcpServers": [
         {
           "name": "chrome-mcp-augment",
           "command": "node",
           "args": ["/path/to/chrome-mcp-new/server/dist/index.js"]
         }
       ]
     }
   }
   ```

3. 重启VS Code以使配置生效

### 使用方法

#### 基本使用
1. 确保Chrome扩展已连接到MCP服务器（popup界面显示"Connected"状态）
2. 在Augment中使用自然语言描述你想要执行的浏览器操作
3. Augment会自动调用相应的浏览器工具来完成任务

#### 示例命令
- "帮我截取当前页面的截图"
- "导航到https://www.google.com"
- "点击页面上的搜索按钮"
- "填写表单中的用户名字段"
- "获取当前页面的文本内容"
- "搜索我的浏览历史记录"
- "添加当前页面到书签"

## 可用工具

### 浏览器管理工具
- `get_windows_and_tabs` - 获取所有浏览器窗口和标签页信息
- `chrome_navigate` - 导航到指定URL
- `chrome_close_tabs` - 关闭指定的标签页
- `chrome_go_back_or_forward` - 浏览器前进/后退

### 页面交互工具
- `chrome_click_element` - 点击页面元素
- `chrome_fill_or_select` - 填写表单或选择选项
- `chrome_keyboard` - 发送键盘输入
- `chrome_get_web_content` - 获取页面内容（文本或HTML）

### 截图工具
- `chrome_screenshot` - 截取页面或元素截图

### 数据管理工具
- `chrome_history` - 搜索浏览历史
- `chrome_bookmark_search` - 搜索书签
- `chrome_bookmark_add` - 添加书签

### 高级功能
- `search_tabs_content` - 跨标签页语义搜索（计划中）

## 故障排除

### 常见问题

#### 1. Chrome扩展无法连接到MCP服务器
- 确保MCP服务器正在运行
- 检查端口8080是否被占用
- 查看Chrome扩展的popup界面状态

#### 2. Augment无法识别MCP服务器
- 确认settings.json中的路径正确
- 重启VS Code
- 检查Node.js版本是否为18+

#### 3. 工具调用失败
- 确保Chrome扩展已连接
- 检查目标页面是否已加载完成
- 查看Chrome开发者工具的控制台错误

### 调试模式
启用调试模式查看详细日志：
```bash
DEBUG=chrome-mcp* node server/dist/index.js
```

## 开发状态

- [x] 项目架构设计
- [x] 基础结构创建
- [x] Chrome扩展开发
- [x] MCP服务器开发
- [x] 通信桥梁实现
- [x] 工具映射
- [x] 测试调试
- [x] 文档编写

## 技术细节

### 架构说明
本项目采用三层架构：
1. **Augment MCP客户端** - 通过stdio与MCP服务器通信
2. **MCP服务器** - 处理MCP协议，通过WebSocket与Chrome扩展通信
3. **Chrome扩展** - 执行具体的浏览器操作

### 通信流程
```
Augment → MCP服务器 (stdio) → Chrome扩展 (WebSocket) → 浏览器API
```

### 安全考虑
- 所有通信都在本地进行，不涉及外部服务器
- Chrome扩展只能访问用户明确授权的页面
- 遵循Chrome扩展的安全策略

## 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
1. Fork本项目
2. 克隆到本地
3. 安装依赖：`cd server && npm install`
4. 构建项目：`npm run build`
5. 加载Chrome扩展进行测试

## 许可证

MIT License
