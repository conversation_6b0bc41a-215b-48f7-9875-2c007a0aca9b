// Chrome MCP Background Script
class ChromeMCPBackground {
    constructor() {
        this.wsConnection = null;
        this.mcpServerPort = 8080;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000;
        this.isConnecting = false;
        
        this.toolsRegistry = new Map();
        this.initializeTools();
        this.setupMessageHandlers();
        
        console.log('Chrome MCP Background initialized');
    }
    
    initializeTools() {
        // 注册所有可用的工具
        const tools = [
            'get_windows_and_tabs',
            'chrome_navigate',
            'chrome_close_tabs',
            'chrome_go_back_or_forward',
            'chrome_screenshot',
            'chrome_network_capture_start',
            'chrome_network_capture_stop',
            'chrome_network_debugger_start',
            'chrome_network_debugger_stop',
            'chrome_network_request',
            'search_tabs_content',
            'chrome_get_web_content',
            'chrome_get_interactive_elements',
            'chrome_click_element',
            'chrome_fill_or_select',
            'chrome_keyboard',
            'chrome_history',
            'chrome_bookmark_search',
            'chrome_bookmark_add',
            'chrome_bookmark_delete',
            'chrome_inject_script',
            'chrome_send_command_to_inject_script'
        ];
        
        tools.forEach(tool => {
            this.toolsRegistry.set(tool, this.createToolHandler(tool));
        });
        
        console.log(`Registered ${tools.length} tools`);
    }
    
    createToolHandler(toolName) {
        return async (args) => {
            console.log(`Executing tool: ${toolName}`, args);
            
            try {
                switch (toolName) {
                    case 'get_windows_and_tabs':
                        return await this.getWindowsAndTabs();
                    case 'chrome_navigate':
                        return await this.navigate(args);
                    case 'chrome_close_tabs':
                        return await this.closeTabs(args);
                    case 'chrome_screenshot':
                        return await this.takeScreenshot(args);
                    case 'chrome_click_element':
                        return await this.clickElement(args);
                    case 'chrome_fill_or_select':
                        return await this.fillOrSelect(args);
                    case 'chrome_get_web_content':
                        return await this.getWebContent(args);
                    case 'chrome_go_back_or_forward':
                        return await this.goBackOrForward(args);
                    case 'chrome_keyboard':
                        return await this.sendKeyboard(args);
                    case 'chrome_history':
                        return await this.searchHistory(args);
                    case 'chrome_bookmark_search':
                        return await this.searchBookmarks(args);
                    case 'chrome_bookmark_add':
                        return await this.addBookmark(args);
                    // 其他工具的实现将在后续添加
                    default:
                        return {
                            content: [{
                                type: 'text',
                                text: `Tool ${toolName} is not yet implemented`
                            }],
                            isError: false
                        };
                }
            } catch (error) {
                console.error(`Error executing tool ${toolName}:`, error);
                return {
                    content: [{
                        type: 'text',
                        text: `Error executing ${toolName}: ${error.message}`
                    }],
                    isError: true
                };
            }
        };
    }
    
    setupMessageHandlers() {
        // 处理来自popup的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });
        
        // 处理扩展启动
        chrome.runtime.onStartup.addListener(() => {
            console.log('Extension started');
            this.attemptConnection();
        });
        
        // 处理扩展安装
        chrome.runtime.onInstalled.addListener(() => {
            console.log('Extension installed');
            this.attemptConnection();
        });
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'GET_STATUS':
                    sendResponse(await this.getStatus());
                    break;
                case 'CONNECT':
                    sendResponse(await this.connect());
                    break;
                case 'DISCONNECT':
                    sendResponse(await this.disconnect());
                    break;
                case 'TEST_CONNECTION':
                    sendResponse(await this.testConnection());
                    break;
                case 'EXECUTE_TOOL':
                    sendResponse(await this.executeTool(message.toolName, message.args));
                    break;
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async getStatus() {
        return {
            mcpConnected: this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN,
            wsConnected: this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN,
            toolsCount: this.toolsRegistry.size,
            serverPort: this.mcpServerPort,
            reconnectAttempts: this.reconnectAttempts
        };
    }
    
    async connect() {
        if (this.isConnecting) {
            return { success: false, error: 'Already connecting' };
        }
        
        return await this.attemptConnection();
    }
    
    async disconnect() {
        if (this.wsConnection) {
            this.wsConnection.close();
            this.wsConnection = null;
        }
        this.reconnectAttempts = 0;
        return { success: true };
    }
    
    async testConnection() {
        if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
            return { success: false, error: 'Not connected' };
        }
        
        return new Promise((resolve) => {
            const testMessage = {
                type: 'ping',
                timestamp: Date.now()
            };
            
            const timeout = setTimeout(() => {
                resolve({ success: false, error: 'Test timeout' });
            }, 5000);
            
            const handlePong = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'pong' && data.timestamp === testMessage.timestamp) {
                        clearTimeout(timeout);
                        this.wsConnection.removeEventListener('message', handlePong);
                        resolve({ success: true });
                    }
                } catch (error) {
                    // 忽略非JSON消息
                }
            };
            
            this.wsConnection.addEventListener('message', handlePong);
            this.wsConnection.send(JSON.stringify(testMessage));
        });
    }
    
    async attemptConnection() {
        if (this.isConnecting) {
            return { success: false, error: 'Already connecting' };
        }
        
        this.isConnecting = true;
        
        try {
            const wsUrl = `ws://localhost:${this.mcpServerPort}/ws`;
            console.log(`Attempting to connect to ${wsUrl}`);
            
            this.wsConnection = new WebSocket(wsUrl);
            
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    this.wsConnection.close();
                    this.isConnecting = false;
                    resolve({ success: false, error: 'Connection timeout' });
                }, 10000);
                
                this.wsConnection.onopen = () => {
                    clearTimeout(timeout);
                    this.isConnecting = false;
                    this.reconnectAttempts = 0;
                    console.log('WebSocket connected');
                    this.setupWebSocketHandlers();
                    resolve({ success: true });
                };
                
                this.wsConnection.onerror = (error) => {
                    clearTimeout(timeout);
                    this.isConnecting = false;
                    console.error('WebSocket error:', error);
                    resolve({ success: false, error: 'Connection failed' });
                };
            });
        } catch (error) {
            this.isConnecting = false;
            console.error('Connection attempt failed:', error);
            return { success: false, error: error.message };
        }
    }
    
    setupWebSocketHandlers() {
        if (!this.wsConnection) return;
        
        this.wsConnection.onmessage = async (event) => {
            try {
                const message = JSON.parse(event.data);
                await this.handleWebSocketMessage(message);
            } catch (error) {
                console.error('Error handling WebSocket message:', error);
            }
        };
        
        this.wsConnection.onclose = () => {
            console.log('WebSocket disconnected');
            this.wsConnection = null;
            this.scheduleReconnect();
        };
        
        this.wsConnection.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }
    
    async handleWebSocketMessage(message) {
        console.log('Received WebSocket message:', message);
        
        if (message.type === 'ping') {
            // 响应ping
            this.wsConnection.send(JSON.stringify({
                type: 'pong',
                timestamp: message.timestamp
            }));
            return;
        }
        
        if (message.type === 'tool_call') {
            // 执行工具调用
            const result = await this.executeTool(message.toolName, message.args);
            
            this.wsConnection.send(JSON.stringify({
                type: 'tool_response',
                id: message.id,
                result: result
            }));
        }
    }
    
    async executeTool(toolName, args) {
        const handler = this.toolsRegistry.get(toolName);
        if (!handler) {
            return {
                content: [{
                    type: 'text',
                    text: `Unknown tool: ${toolName}`
                }],
                isError: true
            };
        }
        
        return await handler(args);
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnect attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            this.attemptConnection();
        }, delay);
    }
    
    // 工具实现方法
    async getWindowsAndTabs() {
        try {
            const windows = await chrome.windows.getAll({ populate: true });
            let tabCount = 0;
            
            const structuredWindows = windows.map(window => {
                const tabs = (window.tabs || []).map(tab => {
                    tabCount++;
                    return {
                        tabId: tab.id || 0,
                        url: tab.url || '',
                        title: tab.title || '',
                        active: tab.active || false
                    };
                });
                
                return {
                    windowId: window.id || 0,
                    tabs
                };
            });
            
            const result = {
                windowCount: windows.length,
                tabCount,
                windows: structuredWindows
            };
            
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify(result)
                }],
                isError: false
            };
        } catch (error) {
            return {
                content: [{
                    type: 'text',
                    text: `Error getting windows and tabs: ${error.message}`
                }],
                isError: true
            };
        }
    }
    
    async navigate(args) {
        const { url, newWindow = false, width, height, refresh = false } = args;

        try {
            if (refresh) {
                const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
                if (!activeTab || !activeTab.id) {
                    return this.createErrorResponse('No active tab found to refresh');
                }
                await chrome.tabs.reload(activeTab.id);
                const updatedTab = await chrome.tabs.get(activeTab.id);
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: 'Successfully refreshed current tab',
                            tabId: updatedTab.id,
                            url: updatedTab.url
                        })
                    }],
                    isError: false
                };
            }

            if (!url) {
                return this.createErrorResponse('URL parameter is required when refresh is not true');
            }

            // Check if URL is already open
            const allTabs = await chrome.tabs.query({});
            const existingTab = allTabs.find(tab => tab.url === url);

            if (existingTab && existingTab.id) {
                await chrome.tabs.update(existingTab.id, { active: true });
                if (existingTab.windowId) {
                    await chrome.windows.update(existingTab.windowId, { focused: true });
                }
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: 'Activated existing tab',
                            tabId: existingTab.id,
                            url: existingTab.url
                        })
                    }],
                    isError: false
                };
            }

            // Open in new window or tab
            if (newWindow || width || height) {
                const newWindowObj = await chrome.windows.create({
                    url,
                    width: width || 1280,
                    height: height || 720,
                    focused: true
                });
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: 'Opened URL in new window',
                            windowId: newWindowObj.id,
                            tabs: newWindowObj.tabs?.map(tab => ({ tabId: tab.id, url: tab.url })) || []
                        })
                    }],
                    isError: false
                };
            } else {
                const newTab = await chrome.tabs.create({ url, active: true });
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: 'Opened URL in new tab',
                            tabId: newTab.id,
                            url: newTab.url
                        })
                    }],
                    isError: false
                };
            }
        } catch (error) {
            return this.createErrorResponse(`Error navigating: ${error.message}`);
        }
    }

    async closeTabs(args) {
        const { tabIds, url } = args;

        try {
            if (url) {
                const tabs = await chrome.tabs.query({ url });
                if (tabs.length === 0) {
                    return {
                        content: [{
                            type: 'text',
                            text: JSON.stringify({
                                success: false,
                                message: `No tabs found with URL: ${url}`,
                                closedCount: 0
                            })
                        }],
                        isError: false
                    };
                }

                const tabIdsToClose = tabs.map(tab => tab.id).filter(id => id !== undefined);
                await chrome.tabs.remove(tabIdsToClose);

                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: `Closed ${tabIdsToClose.length} tabs with URL: ${url}`,
                            closedCount: tabIdsToClose.length,
                            closedTabIds: tabIdsToClose
                        })
                    }],
                    isError: false
                };
            }

            if (tabIds && tabIds.length > 0) {
                const validTabIds = [];
                for (const tabId of tabIds) {
                    try {
                        await chrome.tabs.get(tabId);
                        validTabIds.push(tabId);
                    } catch (error) {
                        // Tab doesn't exist
                    }
                }

                if (validTabIds.length > 0) {
                    await chrome.tabs.remove(validTabIds);
                }

                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: `Closed ${validTabIds.length} tabs`,
                            closedCount: validTabIds.length,
                            closedTabIds: validTabIds
                        })
                    }],
                    isError: false
                };
            }

            // Close active tab if no specific tabs specified
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            await chrome.tabs.remove(activeTab.id);
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: 'Closed active tab',
                        closedCount: 1,
                        closedTabIds: [activeTab.id]
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error closing tabs: ${error.message}`);
        }
    }

    async takeScreenshot(args) {
        const { fullPage = false, selector, quality = 90 } = args;

        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            let screenshotData;

            if (fullPage) {
                // For full page screenshots, we need to capture multiple parts
                screenshotData = await chrome.tabs.captureVisibleTab(activeTab.windowId, {
                    format: 'png',
                    quality: quality
                });
            } else {
                screenshotData = await chrome.tabs.captureVisibleTab(activeTab.windowId, {
                    format: 'png',
                    quality: quality
                });
            }

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: 'Screenshot captured successfully',
                        data: screenshotData,
                        format: 'png',
                        quality: quality,
                        fullPage: fullPage,
                        selector: selector
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error taking screenshot: ${error.message}`);
        }
    }

    createErrorResponse(message) {
        return {
            content: [{
                type: 'text',
                text: message
            }],
            isError: true
        };
    }

    async clickElement(args) {
        const { selector, waitTime = 1000 } = args;

        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            // Inject click helper script
            await this.injectContentScript(activeTab.id, ['inject-scripts/click-helper.js']);

            // Send click command
            const result = await this.sendMessageToTab(activeTab.id, {
                action: 'clickElement',
                selector: selector,
                waitForNavigation: false,
                timeout: 5000
            });

            if (waitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify(result)
                }],
                isError: !!result.error
            };
        } catch (error) {
            return this.createErrorResponse(`Error clicking element: ${error.message}`);
        }
    }

    async fillOrSelect(args) {
        const { selector, value, clear = true } = args;

        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            // Inject fill helper script
            await this.injectContentScript(activeTab.id, ['inject-scripts/fill-helper.js']);

            // Send fill command
            const result = await this.sendMessageToTab(activeTab.id, {
                action: 'fillOrSelect',
                selector: selector,
                value: value,
                clear: clear
            });

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify(result)
                }],
                isError: !!result.error
            };
        } catch (error) {
            return this.createErrorResponse(`Error filling element: ${error.message}`);
        }
    }

    async getWebContent(args) {
        const { format = 'text', selector } = args;

        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            // Inject web fetcher helper script
            await this.injectContentScript(activeTab.id, ['inject-scripts/web-fetcher-helper.js']);

            // Get content
            const action = format === 'html' ? 'getHtmlContent' : 'getTextContent';
            const result = await this.sendMessageToTab(activeTab.id, {
                action: action,
                selector: selector
            });

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify(result)
                }],
                isError: !!result.error
            };
        } catch (error) {
            return this.createErrorResponse(`Error getting web content: ${error.message}`);
        }
    }

    async goBackOrForward(args) {
        const { isForward = false } = args;

        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            if (isForward) {
                await chrome.tabs.goForward(activeTab.id);
            } else {
                await chrome.tabs.goBack(activeTab.id);
            }

            const updatedTab = await chrome.tabs.get(activeTab.id);
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Successfully navigated ${isForward ? 'forward' : 'back'}`,
                        tabId: updatedTab.id,
                        url: updatedTab.url
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error navigating ${isForward ? 'forward' : 'back'}: ${error.message}`);
        }
    }

    async sendKeyboard(args) {
        const { keys, selector } = args;

        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab || !activeTab.id) {
                return this.createErrorResponse('No active tab found');
            }

            // For now, return a placeholder implementation
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Keyboard input "${keys}" sent`,
                        keys: keys,
                        selector: selector
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error sending keyboard input: ${error.message}`);
        }
    }

    async searchHistory(args) {
        const { query = '', maxResults = 50, startTime, endTime } = args;

        try {
            const searchOptions = {
                text: query,
                maxResults: maxResults
            };

            if (startTime) searchOptions.startTime = startTime;
            if (endTime) searchOptions.endTime = endTime;

            const historyItems = await chrome.history.search(searchOptions);

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        results: historyItems,
                        count: historyItems.length,
                        query: query
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error searching history: ${error.message}`);
        }
    }

    async searchBookmarks(args) {
        const { query = '' } = args;

        try {
            const bookmarks = await chrome.bookmarks.search(query);

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        results: bookmarks,
                        count: bookmarks.length,
                        query: query
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error searching bookmarks: ${error.message}`);
        }
    }

    async addBookmark(args) {
        const { title, url, parentId } = args;

        try {
            const bookmark = await chrome.bookmarks.create({
                title: title,
                url: url,
                parentId: parentId
            });

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: 'Bookmark added successfully',
                        bookmark: bookmark
                    })
                }],
                isError: false
            };
        } catch (error) {
            return this.createErrorResponse(`Error adding bookmark: ${error.message}`);
        }
    }
}

// 初始化background script
const chromeMCPBackground = new ChromeMCPBackground();
