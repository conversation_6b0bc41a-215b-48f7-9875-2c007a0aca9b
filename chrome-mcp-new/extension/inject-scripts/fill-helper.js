/* eslint-disable */
// fill-helper.js
// This script is injected into the page to handle form filling operations

if (window.__FILL_HELPER_INITIALIZED__) {
  // Already initialized, skip
} else {
  window.__FILL_HELPER_INITIALIZED__ = true;

  /**
   * Fill or select an element with the given value
   * @param {string} selector - CSS selector for the element
   * @param {string} value - Value to fill or select
   * @param {boolean} clear - Whether to clear the field first
   * @returns {Object} - Result of the fill operation
   */
  async function fillOrSelect(selector, value, clear = true) {
    try {
      const element = document.querySelector(selector);
      if (!element) {
        return {
          error: `Element with selector "${selector}" not found`,
        };
      }

      // Check if element is visible and enabled
      if (!isElementInteractable(element)) {
        return {
          error: `Element with selector "${selector}" is not interactable`,
        };
      }

      // Scroll element into view
      element.scrollIntoView({ behavior: 'auto', block: 'center', inline: 'center' });
      await new Promise(resolve => setTimeout(resolve, 100));

      // Focus the element
      element.focus();

      const tagName = element.tagName.toLowerCase();
      const elementType = element.type ? element.type.toLowerCase() : '';
      
      let result = {};

      if (tagName === 'select') {
        result = await handleSelectElement(element, value);
      } else if (tagName === 'input') {
        result = await handleInputElement(element, value, clear, elementType);
      } else if (tagName === 'textarea') {
        result = await handleTextareaElement(element, value, clear);
      } else if (element.contentEditable === 'true') {
        result = await handleContentEditableElement(element, value, clear);
      } else {
        return {
          error: `Element type "${tagName}" is not supported for filling`,
        };
      }

      // Trigger change events
      triggerChangeEvents(element);

      return {
        success: true,
        message: 'Element filled successfully',
        elementInfo: {
          tagName: element.tagName,
          type: element.type || null,
          id: element.id,
          className: element.className,
          value: element.value || element.textContent,
        },
        ...result,
      };
    } catch (error) {
      return {
        error: `Error filling element: ${error.message}`,
      };
    }
  }

  /**
   * Handle select element
   */
  async function handleSelectElement(element, value) {
    const options = Array.from(element.options);
    let selectedOption = null;

    // Try to find option by value
    selectedOption = options.find(option => option.value === value);
    
    // If not found, try to find by text content
    if (!selectedOption) {
      selectedOption = options.find(option => 
        option.textContent.trim().toLowerCase().includes(value.toLowerCase())
      );
    }

    if (selectedOption) {
      element.value = selectedOption.value;
      selectedOption.selected = true;
      return { selectedValue: selectedOption.value, selectedText: selectedOption.textContent };
    } else {
      return { error: `Option "${value}" not found in select element` };
    }
  }

  /**
   * Handle input element
   */
  async function handleInputElement(element, value, clear, elementType) {
    if (elementType === 'checkbox' || elementType === 'radio') {
      const shouldCheck = value === 'true' || value === true || value === '1' || value === 1;
      element.checked = shouldCheck;
      return { checked: shouldCheck };
    } else if (elementType === 'file') {
      return { error: 'File input filling is not supported for security reasons' };
    } else {
      // Text-based inputs
      if (clear) {
        element.value = '';
      }
      
      // Type the value character by character to trigger input events
      for (const char of value) {
        element.value += char;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      return { filledValue: element.value };
    }
  }

  /**
   * Handle textarea element
   */
  async function handleTextareaElement(element, value, clear) {
    if (clear) {
      element.value = '';
    }
    
    // Type the value character by character
    for (const char of value) {
      element.value += char;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    return { filledValue: element.value };
  }

  /**
   * Handle content editable element
   */
  async function handleContentEditableElement(element, value, clear) {
    if (clear) {
      element.textContent = '';
    }
    
    element.textContent += value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    
    return { filledValue: element.textContent };
  }

  /**
   * Check if element is interactable
   */
  function isElementInteractable(element) {
    const style = window.getComputedStyle(element);
    
    if (style.display === 'none' || style.visibility === 'hidden') {
      return false;
    }
    
    if (element.disabled || element.readOnly) {
      return false;
    }
    
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      return false;
    }
    
    return true;
  }

  /**
   * Trigger change events on the element
   */
  function triggerChangeEvents(element) {
    const events = ['input', 'change', 'blur'];
    events.forEach(eventType => {
      element.dispatchEvent(new Event(eventType, { bubbles: true }));
    });
  }

  // Listen for messages from the extension
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === 'fillOrSelect') {
      fillOrSelect(request.selector, request.value, request.clear)
        .then(sendResponse)
        .catch((error) => {
          sendResponse({
            error: `Unexpected error: ${error.message}`,
          });
        });
      return true; // Indicates async response
    } else if (request.action === 'chrome_fill_helper_ping') {
      sendResponse({ status: 'pong' });
      return false;
    }
  });
}
