/* eslint-disable */
// web-fetcher-helper.js
// This script is injected into the page to extract web content

if (window.__WEB_FETCHER_HELPER_INITIALIZED__) {
  // Already initialized, skip
} else {
  window.__WEB_FETCHER_HELPER_INITIALIZED__ = true;

  /**
   * Get HTML content from the page
   * @param {string} selector - Optional CSS selector to get content from specific element
   * @returns {Object} - HTML content and metadata
   */
  function getHtmlContent(selector = null) {
    try {
      let element = selector ? document.querySelector(selector) : document;
      let content = '';
      
      if (selector && !element) {
        return {
          error: `Element with selector "${selector}" not found`,
        };
      }

      if (selector) {
        content = element.outerHTML;
      } else {
        content = document.documentElement.outerHTML;
      }

      return {
        success: true,
        content: content,
        title: document.title,
        url: window.location.href,
        selector: selector,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        error: `Error getting HTML content: ${error.message}`,
      };
    }
  }

  /**
   * Get text content from the page
   * @param {string} selector - Optional CSS selector to get content from specific element
   * @returns {Object} - Text content and metadata
   */
  function getTextContent(selector = null) {
    try {
      let element = selector ? document.querySelector(selector) : document.body;
      let content = '';
      
      if (selector && !element) {
        return {
          error: `Element with selector "${selector}" not found`,
        };
      }

      if (selector) {
        content = element.textContent || element.innerText || '';
      } else {
        // Get clean text content from the page
        content = extractCleanText();
      }

      return {
        success: true,
        content: content.trim(),
        title: document.title,
        url: window.location.href,
        selector: selector,
        wordCount: content.trim().split(/\s+/).length,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        error: `Error getting text content: ${error.message}`,
      };
    }
  }

  /**
   * Extract clean text content from the page
   * @returns {string} - Clean text content
   */
  function extractCleanText() {
    // Clone the document to avoid modifying the original
    const clone = document.cloneNode(true);
    
    // Remove script and style elements
    const scriptsAndStyles = clone.querySelectorAll('script, style, noscript');
    scriptsAndStyles.forEach(el => el.remove());
    
    // Remove hidden elements
    const hiddenElements = clone.querySelectorAll('[style*="display: none"], [style*="visibility: hidden"]');
    hiddenElements.forEach(el => el.remove());
    
    // Get text content
    let text = clone.body ? clone.body.textContent || clone.body.innerText || '' : '';
    
    // Clean up whitespace
    text = text.replace(/\s+/g, ' ').trim();
    
    return text;
  }

  /**
   * Get page metadata
   * @returns {Object} - Page metadata
   */
  function getPageMetadata() {
    try {
      const metadata = {
        title: document.title,
        url: window.location.href,
        domain: window.location.hostname,
        path: window.location.pathname,
        description: '',
        keywords: '',
        author: '',
        language: document.documentElement.lang || 'en',
        charset: document.characterSet || 'UTF-8',
        timestamp: Date.now(),
      };

      // Extract meta tags
      const metaTags = document.querySelectorAll('meta');
      metaTags.forEach(meta => {
        const name = meta.getAttribute('name') || meta.getAttribute('property');
        const content = meta.getAttribute('content');
        
        if (name && content) {
          switch (name.toLowerCase()) {
            case 'description':
            case 'og:description':
              metadata.description = content;
              break;
            case 'keywords':
              metadata.keywords = content;
              break;
            case 'author':
              metadata.author = content;
              break;
          }
        }
      });

      return {
        success: true,
        metadata: metadata,
      };
    } catch (error) {
      return {
        error: `Error getting page metadata: ${error.message}`,
      };
    }
  }

  // Listen for messages from the extension
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    switch (request.action) {
      case 'getHtmlContent':
        sendResponse(getHtmlContent(request.selector));
        break;
      case 'getTextContent':
        sendResponse(getTextContent(request.selector));
        break;
      case 'getPageMetadata':
        sendResponse(getPageMetadata());
        break;
      case 'chrome_web_fetcher_ping':
        sendResponse({ status: 'pong' });
        break;
      default:
        sendResponse({ error: 'Unknown action' });
    }
    return false;
  });
}
