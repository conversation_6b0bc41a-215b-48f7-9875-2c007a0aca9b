{"manifest_version": 3, "name": "Chrome MCP 服务器 for Augment", "description": "兼容 Augment MCP 的 Chrome 扩展，支持浏览器自动化和内容分析", "version": "1.0.0", "icons": {"16": "icon/16.png", "32": "icon/32.png", "48": "icon/48.png", "96": "icon/96.png", "128": "icon/128.png"}, "permissions": ["tabs", "activeTab", "scripting", "downloads", "webRequest", "debugger", "history", "bookmarks", "offscreen", "storage"], "host_permissions": ["<all_urls>"], "web_accessible_resources": [{"resources": ["/models/*", "/workers/*", "/inject-scripts/*"], "matches": ["<all_urls>"]}], "cross_origin_embedder_policy": {"value": "require-corp"}, "cross_origin_opener_policy": {"value": "same-origin"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}, "background": {"service_worker": "background.js"}, "action": {"default_title": "Chrome MCP for Augment", "default_popup": "popup.html"}, "content_scripts": [{"matches": ["*://*.google.com/*"], "js": ["content-scripts/content.js"]}]}