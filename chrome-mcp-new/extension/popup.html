<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome MCP for Augment</title>
    <link rel="stylesheet" href="assets/popup.css">
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .logo {
            width: 48px;
            height: 48px;
            margin: 0 auto 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        
        .subtitle {
            font-size: 12px;
            color: #64748b;
            margin: 4px 0 0 0;
        }
        
        .status-section {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }
        
        .status-value {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .status-connected {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-disconnected {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }
        
        .actions-section {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .action-button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s;
        }
        
        .action-button:last-child {
            margin-bottom: 0;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }
        
        .btn-secondary:hover {
            background: #e2e8f0;
        }
        
        .info-section {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            color: #64748b;
        }
        
        .info-value {
            color: #1e293b;
            font-weight: 500;
        }
        
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">C</div>
        <h1 class="title">Chrome MCP</h1>
        <p class="subtitle">兼容 Augment</p>
    </div>
    
    <div class="status-section">
        <div class="status-item">
            <span class="status-label">MCP 服务器</span>
            <span id="mcp-status" class="status-value status-disconnected">未连接</span>
        </div>
        <div class="status-item">
            <span class="status-label">WebSocket</span>
            <span id="ws-status" class="status-value status-disconnected">未连接</span>
        </div>
        <div class="status-item">
            <span class="status-label">可用工具</span>
            <span id="tools-count" class="status-value status-pending">0</span>
        </div>
    </div>
    
    <div class="actions-section">
        <button id="connect-btn" class="action-button btn-primary">连接到 MCP 服务器</button>
        <button id="test-btn" class="action-button btn-secondary" disabled>测试连接</button>
        <button id="refresh-btn" class="action-button btn-secondary">刷新状态</button>
    </div>
    
    <div class="info-section">
        <div class="info-item">
            <span class="info-label">版本</span>
            <span class="info-value">1.0.0</span>
        </div>
        <div class="info-item">
            <span class="info-label">端口</span>
            <span class="info-value" id="server-port">8080</span>
        </div>
        <div class="info-item">
            <span class="info-label">最后更新</span>
            <span class="info-value" id="last-update">从未</span>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
