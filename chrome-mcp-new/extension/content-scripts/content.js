// Content script for Chrome MCP extension
// This script runs in the context of web pages

(function() {
    'use strict';
    
    // Prevent multiple initialization
    if (window.__CHROME_MCP_CONTENT_INITIALIZED__) {
        return;
    }
    window.__CHROME_MCP_CONTENT_INITIALIZED__ = true;
    
    console.log('Chrome MCP content script initialized');
    
    // Listen for messages from the background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('Content script received message:', request);
        
        switch (request.action) {
            case 'ping':
                sendResponse({ status: 'pong', timestamp: Date.now() });
                break;
                
            case 'getPageInfo':
                sendResponse(getPageInfo());
                break;
                
            case 'getInteractiveElements':
                sendResponse(getInteractiveElements());
                break;
                
            default:
                sendResponse({ error: 'Unknown action in content script' });
        }
        
        return false; // Synchronous response
    });
    
    /**
     * Get basic page information
     */
    function getPageInfo() {
        try {
            return {
                success: true,
                title: document.title,
                url: window.location.href,
                domain: window.location.hostname,
                readyState: document.readyState,
                timestamp: Date.now()
            };
        } catch (error) {
            return {
                error: `Error getting page info: ${error.message}`
            };
        }
    }
    
    /**
     * Get interactive elements on the page
     */
    function getInteractiveElements() {
        try {
            const interactiveSelectors = [
                'a[href]',
                'button',
                'input',
                'select',
                'textarea',
                '[onclick]',
                '[role="button"]',
                '[tabindex]'
            ];
            
            const elements = [];
            
            interactiveSelectors.forEach(selector => {
                const found = document.querySelectorAll(selector);
                found.forEach((element, index) => {
                    if (isElementVisible(element)) {
                        const rect = element.getBoundingClientRect();
                        elements.push({
                            tagName: element.tagName.toLowerCase(),
                            type: element.type || null,
                            id: element.id || null,
                            className: element.className || null,
                            text: element.textContent?.trim().substring(0, 100) || '',
                            href: element.href || null,
                            selector: generateSelector(element),
                            rect: {
                                x: rect.x,
                                y: rect.y,
                                width: rect.width,
                                height: rect.height
                            },
                            isVisible: true
                        });
                    }
                });
            });
            
            return {
                success: true,
                elements: elements.slice(0, 100), // Limit to first 100 elements
                count: elements.length,
                timestamp: Date.now()
            };
        } catch (error) {
            return {
                error: `Error getting interactive elements: ${error.message}`
            };
        }
    }
    
    /**
     * Check if an element is visible
     */
    function isElementVisible(element) {
        if (!element) return false;
        
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
            return false;
        }
        
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }
        
        // Check if element is in viewport
        if (rect.bottom < 0 || rect.top > window.innerHeight || 
            rect.right < 0 || rect.left > window.innerWidth) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate a CSS selector for an element
     */
    function generateSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
            }
        }
        
        // Fallback to tag name with nth-child
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.children);
            const index = siblings.indexOf(element) + 1;
            return `${element.tagName.toLowerCase()}:nth-child(${index})`;
        }
        
        return element.tagName.toLowerCase();
    }
    
    // Notify background script that content script is ready
    chrome.runtime.sendMessage({
        action: 'content_script_ready',
        url: window.location.href,
        timestamp: Date.now()
    }).catch(error => {
        console.log('Could not send ready message:', error);
    });
    
})();
