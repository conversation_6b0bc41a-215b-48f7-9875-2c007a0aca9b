// Chrome MCP Popup Script
class PopupManager {
    constructor() {
        this.mcpStatus = document.getElementById('mcp-status');
        this.wsStatus = document.getElementById('ws-status');
        this.toolsCount = document.getElementById('tools-count');
        this.connectBtn = document.getElementById('connect-btn');
        this.testBtn = document.getElementById('test-btn');
        this.refreshBtn = document.getElementById('refresh-btn');
        this.serverPort = document.getElementById('server-port');
        this.lastUpdate = document.getElementById('last-update');
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadStatus();
        this.startStatusPolling();
    }
    
    bindEvents() {
        this.connectBtn.addEventListener('click', () => this.handleConnect());
        this.testBtn.addEventListener('click', () => this.handleTest());
        this.refreshBtn.addEventListener('click', () => this.handleRefresh());
    }
    
    async loadStatus() {
        try {
            // 从background script获取状态
            const response = await chrome.runtime.sendMessage({
                action: 'GET_STATUS'
            });
            
            if (response) {
                this.updateStatus(response);
            }
        } catch (error) {
            console.error('Failed to load status:', error);
            this.updateStatus({
                mcpConnected: false,
                wsConnected: false,
                toolsCount: 0,
                serverPort: 8080
            });
        }
    }
    
    updateStatus(status) {
        // 更新MCP服务器状态
        if (status.mcpConnected) {
            this.mcpStatus.textContent = '已连接';
            this.mcpStatus.className = 'status-value status-connected';
        } else {
            this.mcpStatus.textContent = '未连接';
            this.mcpStatus.className = 'status-value status-disconnected';
        }

        // 更新WebSocket状态
        if (status.wsConnected) {
            this.wsStatus.textContent = '已连接';
            this.wsStatus.className = 'status-value status-connected';
        } else {
            this.wsStatus.textContent = '未连接';
            this.wsStatus.className = 'status-value status-disconnected';
        }
        
        // 更新工具数量
        this.toolsCount.textContent = status.toolsCount || 0;
        this.toolsCount.className = `status-value ${status.toolsCount > 0 ? 'status-connected' : 'status-pending'}`;
        
        // 更新服务器端口
        if (status.serverPort) {
            this.serverPort.textContent = status.serverPort;
        }
        
        // 更新按钮状态
        this.updateButtons(status);
        
        // 更新最后更新时间
        this.lastUpdate.textContent = new Date().toLocaleTimeString();
    }
    
    updateButtons(status) {
        const isConnected = status.mcpConnected && status.wsConnected;

        if (isConnected) {
            this.connectBtn.textContent = '断开连接';
            this.connectBtn.className = 'action-button btn-secondary';
            this.testBtn.disabled = false;
        } else {
            this.connectBtn.textContent = '连接到 MCP 服务器';
            this.connectBtn.className = 'action-button btn-primary';
            this.testBtn.disabled = true;
        }
    }
    
    async handleConnect() {
        const isConnected = this.mcpStatus.textContent === 'Connected';
        
        try {
            this.setLoading(this.connectBtn, true);
            
            const action = isConnected ? 'DISCONNECT' : 'CONNECT';
            const response = await chrome.runtime.sendMessage({ action });
            
            if (response && response.success) {
                await this.loadStatus();
            } else {
                throw new Error(response?.error || '连接失败');
            }
        } catch (error) {
            console.error('Connection error:', error);
            this.showError('连接失败: ' + error.message);
        } finally {
            this.setLoading(this.connectBtn, false);
        }
    }
    
    async handleTest() {
        try {
            this.setLoading(this.testBtn, true);
            
            const response = await chrome.runtime.sendMessage({
                action: 'TEST_CONNECTION'
            });
            
            if (response && response.success) {
                this.showSuccess('连接测试成功！');
            } else {
                throw new Error(response?.error || '测试失败');
            }
        } catch (error) {
            console.error('Test error:', error);
            this.showError('测试失败: ' + error.message);
        } finally {
            this.setLoading(this.testBtn, false);
        }
    }
    
    async handleRefresh() {
        try {
            this.setLoading(this.refreshBtn, true);
            await this.loadStatus();
        } catch (error) {
            console.error('Refresh error:', error);
            this.showError('刷新失败: ' + error.message);
        } finally {
            this.setLoading(this.refreshBtn, false);
        }
    }
    
    setLoading(button, loading) {
        if (loading) {
            button.innerHTML = '<span class="loading"></span> 加载中...';
            button.disabled = true;
        } else {
            // 恢复原始文本
            if (button === this.connectBtn) {
                const isConnected = this.mcpStatus.textContent === '已连接';
                button.textContent = isConnected ? '断开连接' : '连接到 MCP 服务器';
            } else if (button === this.testBtn) {
                button.textContent = '测试连接';
            } else if (button === this.refreshBtn) {
                button.textContent = '刷新状态';
            }
            button.disabled = false;
        }
    }
    
    showError(message) {
        // 简单的错误提示
        const originalText = this.mcpStatus.textContent;
        this.mcpStatus.textContent = '错误';
        this.mcpStatus.className = 'status-value status-disconnected';

        setTimeout(() => {
            this.loadStatus();
        }, 2000);
    }

    showSuccess(message) {
        // 简单的成功提示
        console.log('成功:', message);
    }
    
    startStatusPolling() {
        // 每5秒更新一次状态
        setInterval(() => {
            this.loadStatus();
        }, 5000);
    }
}

// 初始化popup管理器
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
